import React from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { BrowserRouter } from 'react-router-dom'
import StoreContextProvider from './context/StoreContext.jsx'
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary.jsx'
import { registerSW, requestNotificationPermission } from './utils/serviceWorker'

// Ensure DOM is ready before initializing React
function initializeApp() {
  // Get root element
  const rootElement = document.getElementById('root')

  if (!rootElement) {
    console.error('❌ Root element not found')
    return
  }

  try {
    // Create root and render app with proper error handling
    const root = createRoot(rootElement)

    root.render(
      <ErrorBoundary>
        <BrowserRouter>
          <StoreContextProvider>
            <App />
          </StoreContextProvider>
        </BrowserRouter>
      </ErrorBoundary>
    )

    console.log('✅ EatZone app initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize EatZone app:', error)
  }
}

// Initialize app when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp)
} else {
  initializeApp()
}

// Register service worker for aggressive image caching and offline support
// Enable in both development and production for faster image loading
try {
  registerSW();
  if (import.meta.env.PROD) {
    requestNotificationPermission();
  }
} catch (error) {
  console.error('❌ Service worker registration failed:', error);
}
